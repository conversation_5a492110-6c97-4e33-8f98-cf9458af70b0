// B+树动画步骤类型定义
export type AnimationStep = 
  | { type: 'traverse', nodeId: string, path: string[] }
  | { type: 'insert_key', nodeId: string, key: number }
  | { type: 'split', originalNodeId: string, newNodeId: string, promotedKey: number }
  | { type: 'delete_key', nodeId: string, key: number }
  | { type: 'merge', nodeId1: string, nodeId2: string, resultNodeId: string }
  | { type: 'redistribute', fromNodeId: string, toNodeId: string, key: number }
  | { type: 'update_parent', nodeId: string, newKey: number };

// B+树节点接口
export interface BPlusTreeNode {
  id: string;
  keys: number[];
  pointers: (string | null)[];
  isLeaf: boolean;
  level: number;
  parent?: string | null;
  next?: string | null; // 叶子节点的兄弟指针
}

// B+树类
export class BPlusTree {
  private order: number;
  private root: BPlusTreeNode | null = null;
  private nodeCounter = 0;
  private allNodes: Map<string, BPlusTreeNode> = new Map();

  constructor(order: number = 3) {
    this.order = order;
  }

  // 创建新节点
  private createNode(isLeaf: boolean, level: number): BPlusTreeNode {
    const node: BPlusTreeNode = {
      id: `node-${this.nodeCounter++}`,
      keys: [],
      pointers: [],
      isLeaf,
      level,
      parent: null,
      next: null
    };

    this.allNodes.set(node.id, node);
    return node;
  }

  // 查找叶子节点
  private *findLeafNode(key: number): Generator<AnimationStep, BPlusTreeNode, unknown> {
    if (!this.root) {
      throw new Error('树为空');
    }

    let current = this.root;
    const path: string[] = [current.id];

    while (!current.isLeaf) {
      yield { type: 'traverse', nodeId: current.id, path: [...path] };

      // 找到合适的子节点
      let childIndex = 0;
      while (childIndex < current.keys.length && key >= current.keys[childIndex]) {
        childIndex++;
      }

      const childId = current.pointers[childIndex];
      if (!childId || !this.allNodes.has(childId)) {
        throw new Error('无效的子节点指针');
      }

      current = this.allNodes.get(childId)!;
      path.push(current.id);
    }

    yield { type: 'traverse', nodeId: current.id, path: [...path] };
    return current;
  }

  // 插入键值的生成器函数
  public *insert(key: number): Generator<AnimationStep, void, unknown> {
    // 如果树为空，创建根节点
    if (!this.root) {
      this.root = this.createNode(true, 0);
      this.root.keys.push(key);
      yield { type: 'insert_key', nodeId: this.root.id, key };
      return;
    }

    // 阶段1：查找阶段 - 完整执行findLeafNode生成器
    const findGenerator = this.findLeafNode(key);
    let findResult = findGenerator.next();

    // 消费所有traverse步骤
    while (!findResult.done) {
      yield findResult.value; // yield traverse步骤
      findResult = findGenerator.next();
    }

    // 获取查找结果：叶子节点
    const leafNode = findResult.value;

    // 检查键是否已存在
    if (leafNode.keys.includes(key)) {
      throw new Error(`键 ${key} 已存在`);
    }

    // 阶段2：更新阶段 - 插入键值并统一调用结构性修复
    yield { type: 'insert_key', nodeId: leafNode.id, key };
    this.insertKeyIntoNode(leafNode, key);

    // 统一调用结构性修复函数
    yield* this.checkAndFixNode(leafNode);
  }

  // 在节点中插入键（保持有序）
  private insertKeyIntoNode(node: BPlusTreeNode, key: number): void {
    let insertIndex = 0;
    while (insertIndex < node.keys.length && node.keys[insertIndex] < key) {
      insertIndex++;
    }
    node.keys.splice(insertIndex, 0, key);
  }

  // 获取从根到指定节点的路径
  private getPathToNode(nodeId: string): BPlusTreeNode[] {
    const path: BPlusTreeNode[] = [];
    let current = this.allNodes.get(nodeId);

    if (!current) return path;

    // 从叶子节点向上构建路径
    while (current) {
      path.unshift(current);
      if (current.parent) {
        current = this.allNodes.get(current.parent);
      } else {
        break;
      }
    }

    return path;
  }

  // 统一的节点结构性检查和修复函数
  private *checkAndFixNode(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    // 情况1：节点key数超过最大值 - 执行分裂
    if (node.keys.length >= this.order) {
      yield* this.splitNodeAndPropagate(node);
      return;
    }

    // 情况2：非根节点key数少于最小值 - 执行借位或合并
    const minKeys = Math.ceil((this.order - 1) / 2);
    if (node !== this.root && node.keys.length < minKeys) {
      if (this.canBorrowFromSibling(node)) {
        yield* this.borrowFromSibling(node);
      } else {
        yield* this.mergeWithSibling(node);
      }
      return;
    }

    // 情况3：检查并更新父节点的分割键
    if (this.isFirstKeyChanged(node)) {
      yield* this.updateAncestorKeys(node);
    }

    // 情况4：根节点特殊处理 - 如果根节点key数为0且有子节点，提升子节点为新根
    if (node === this.root && node.keys.length === 0 && node.pointers.length > 0) {
      const newRootId = node.pointers[0];
      if (newRootId && this.allNodes.has(newRootId)) {
        const newRoot = this.allNodes.get(newRootId)!;
        newRoot.parent = null;
        this.allNodes.delete(node.id);
        this.root = newRoot;
      }
    }
  }

  // 分裂节点并向上传播
  private *splitNodeAndPropagate(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    const splitResult = this.performSplit(node);
    yield {
      type: 'split',
      originalNodeId: node.id,
      newNodeId: splitResult.newNode.id,
      promotedKey: splitResult.promotedKey
    };

    // 处理父节点
    if (!node.parent) {
      // 根节点分裂，创建新根
      const newRoot = this.createNode(false, node.level + 1);
      newRoot.keys.push(splitResult.promotedKey);
      newRoot.pointers.push(node.id, splitResult.newNode.id);
      node.parent = newRoot.id;
      splitResult.newNode.parent = newRoot.id;
      this.root = newRoot;
    } else {
      // 向父节点插入提升的键
      const parent = this.allNodes.get(node.parent)!;
      this.insertKeyIntoNode(parent, splitResult.promotedKey);

      // 找到插入位置并插入指针
      let insertIndex = 0;
      while (insertIndex < parent.keys.length - 1 && splitResult.promotedKey > parent.keys[insertIndex]) {
        insertIndex++;
      }
      parent.pointers.splice(insertIndex + 1, 0, splitResult.newNode.id);
      splitResult.newNode.parent = parent.id;

      // 递归检查父节点
      yield* this.checkAndFixNode(parent);
    }
  }

  // 执行节点分裂操作
  private performSplit(node: BPlusTreeNode): { newNode: BPlusTreeNode; promotedKey: number } {
    if (node.isLeaf) {
      // 叶子节点分裂
      const mid = Math.ceil(node.keys.length / 2);
      const newNode = this.createNode(true, node.level);
      newNode.keys = node.keys.splice(mid);
      newNode.next = node.next;
      node.next = newNode.id;
      newNode.parent = node.parent;
      const promotedKey = newNode.keys[0];
      return { newNode, promotedKey };
    } else {
      // 内部节点分裂
      const mid = Math.floor(node.keys.length / 2);
      const newNode = this.createNode(false, node.level);
      const promotedKey = node.keys[mid];
      newNode.keys = node.keys.splice(mid + 1);
      newNode.pointers = node.pointers.splice(mid + 1);
      node.keys.splice(mid, 1); // 移除提升的键
      newNode.parent = node.parent;

      // 更新子节点的父指针
      newNode.pointers.forEach(pointerId => {
        if (pointerId) {
          const child = this.allNodes.get(pointerId);
          if (child) child.parent = newNode.id;
        }
      });

      return { newNode, promotedKey };
    }
  }



  // 删除键值的生成器函数
  public *delete(key: number): Generator<AnimationStep, void, unknown> {
    if (!this.root) {
      throw new Error('树为空');
    }

    // 阶段1：查找阶段 - 完整执行findLeafNode生成器
    const findGenerator = this.findLeafNode(key);
    let findResult = findGenerator.next();

    // 消费所有traverse步骤
    while (!findResult.done) {
      yield findResult.value; // yield traverse步骤
      findResult = findGenerator.next();
    }

    // 获取查找结果：叶子节点
    const leafNode = findResult.value;

    // 检查键是否存在
    const keyIndex = leafNode.keys.indexOf(key);
    if (keyIndex === -1) {
      throw new Error(`键 ${key} 不存在`);
    }

    // 阶段2：更新阶段 - 删除键值并统一调用结构性修复
    yield { type: 'delete_key', nodeId: leafNode.id, key };

    // 记录是否删除的是首个键，用于后续的索引更新判断
    const isFirstKeyDeleted = keyIndex === 0;
    leafNode.keys.splice(keyIndex, 1);

    // 如果删除的是第一个键且节点还有其他键，需要更新父节点索引
    if (leafNode.keys.length > 0 && leafNode.parent && isFirstKeyDeleted) {
      yield* this.updateAncestorKeys(leafNode);
    }

    // 统一调用结构性修复函数
    yield* this.checkAndFixNode(leafNode);
  }







  // 检查是否可以从兄弟节点借位
  private canBorrowFromSibling(node: BPlusTreeNode): boolean {
    if (!node.parent) return false;

    const parent = this.allNodes.get(node.parent)!;
    const nodeIndex = parent.pointers.indexOf(node.id);
    const minKeys = Math.ceil((this.order - 1) / 2);

    // 检查右兄弟
    const rightSiblingId = parent.pointers[nodeIndex + 1];
    if (rightSiblingId) {
      const rightSibling = this.allNodes.get(rightSiblingId)!;
      if (rightSibling.keys.length > minKeys) {
        return true;
      }
    }

    // 检查左兄弟
    const leftSiblingId = parent.pointers[nodeIndex - 1];
    if (leftSiblingId) {
      const leftSibling = this.allNodes.get(leftSiblingId)!;
      if (leftSibling.keys.length > minKeys) {
        return true;
      }
    }

    return false;
  }

  // 从兄弟节点借位
  private *borrowFromSibling(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    if (!node.parent) return;

    const parent = this.allNodes.get(node.parent)!;
    const nodeIndex = parent.pointers.indexOf(node.id);
    const minKeys = Math.ceil((this.order - 1) / 2);

    // 优先从右兄弟借位
    const rightSiblingId = parent.pointers[nodeIndex + 1];
    if (rightSiblingId) {
      const rightSibling = this.allNodes.get(rightSiblingId)!;
      if (rightSibling.keys.length > minKeys) {
        yield* this.borrowFromRightSibling(node, rightSibling, parent, nodeIndex);
        return;
      }
    }

    // 从左兄弟借位
    const leftSiblingId = parent.pointers[nodeIndex - 1];
    if (leftSiblingId) {
      const leftSibling = this.allNodes.get(leftSiblingId)!;
      if (leftSibling.keys.length > minKeys) {
        yield* this.borrowFromLeftSibling(node, leftSibling, parent, nodeIndex);
        return;
      }
    }
  }

  // 与兄弟节点合并
  private *mergeWithSibling(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    if (!node.parent) return;

    const parent = this.allNodes.get(node.parent)!;
    const nodeIndex = parent.pointers.indexOf(node.id);

    // 优先与右兄弟合并
    const rightSiblingId = parent.pointers[nodeIndex + 1];
    if (rightSiblingId) {
      const rightSibling = this.allNodes.get(rightSiblingId)!;
      yield* this.mergeWithRightSibling(node, rightSibling, parent, nodeIndex);
      // 递归检查父节点
      yield* this.checkAndFixNode(parent);
      return;
    }

    // 与左兄弟合并
    const leftSiblingId = parent.pointers[nodeIndex - 1];
    if (leftSiblingId) {
      const leftSibling = this.allNodes.get(leftSiblingId)!;
      yield* this.mergeWithLeftSibling(node, leftSibling, parent, nodeIndex);
      // 递归检查父节点
      yield* this.checkAndFixNode(parent);
      return;
    }
  }

  // 从右兄弟借键
  private *borrowFromRightSibling(node: BPlusTreeNode, rightSibling: BPlusTreeNode,
    parent: BPlusTreeNode, nodeIndex: number): Generator<AnimationStep, void, unknown> {
    const keyToMove = rightSibling.keys.shift()!;
    yield { type: 'redistribute', fromNodeId: rightSibling.id, toNodeId: node.id, key: keyToMove };

    if (node.isLeaf) {
      node.keys.push(keyToMove);
      parent.keys[nodeIndex] = rightSibling.keys[0];
    } else {
      const pointerToMove = rightSibling.pointers.shift()!;
      node.keys.push(parent.keys[nodeIndex]);
      parent.keys[nodeIndex] = keyToMove;
      node.pointers.push(pointerToMove);
      const child = this.allNodes.get(pointerToMove);
      if (child) child.parent = node.id;
    }

    yield { type: 'update_parent', nodeId: parent.id, newKey: parent.keys[nodeIndex] };
  }

  // 从左兄弟借键
  private *borrowFromLeftSibling(node: BPlusTreeNode, leftSibling: BPlusTreeNode,
    parent: BPlusTreeNode, nodeIndex: number): Generator<AnimationStep, void, unknown> {
    const keyToMove = leftSibling.keys.pop()!;
    yield { type: 'redistribute', fromNodeId: leftSibling.id, toNodeId: node.id, key: keyToMove };

    if (node.isLeaf) {
      node.keys.unshift(keyToMove);
      parent.keys[nodeIndex - 1] = node.keys[0];
    } else {
      const pointerToMove = leftSibling.pointers.pop()!;
      node.keys.unshift(parent.keys[nodeIndex - 1]);
      parent.keys[nodeIndex - 1] = keyToMove;
      node.pointers.unshift(pointerToMove);
      const child = this.allNodes.get(pointerToMove);
      if (child) child.parent = node.id;
    }

    yield { type: 'update_parent', nodeId: parent.id, newKey: parent.keys[nodeIndex - 1] };
  }

  // 与右兄弟合并
  private *mergeWithRightSibling(node: BPlusTreeNode, rightSibling: BPlusTreeNode,
    parent: BPlusTreeNode, nodeIndex: number): Generator<AnimationStep, void, unknown> {
    yield { type: 'merge', nodeId1: node.id, nodeId2: rightSibling.id, resultNodeId: node.id };

    if (node.isLeaf) {
      node.keys.push(...rightSibling.keys);
      node.next = rightSibling.next;
    } else {
      node.keys.push(parent.keys[nodeIndex]);
      node.keys.push(...rightSibling.keys);
      node.pointers.push(...rightSibling.pointers);

      // 更新子节点的父指针
      rightSibling.pointers.forEach(pointerId => {
        if (pointerId) {
          const child = this.allNodes.get(pointerId);
          if (child) child.parent = node.id;
        }
      });
    }

    parent.keys.splice(nodeIndex, 1);
    parent.pointers.splice(nodeIndex + 1, 1);
    this.allNodes.delete(rightSibling.id);
  }

  // 与左兄弟合并
  private *mergeWithLeftSibling(node: BPlusTreeNode, leftSibling: BPlusTreeNode,
    parent: BPlusTreeNode, nodeIndex: number): Generator<AnimationStep, void, unknown> {
    yield { type: 'merge', nodeId1: leftSibling.id, nodeId2: node.id, resultNodeId: leftSibling.id };

    if (node.isLeaf) {
      leftSibling.keys.push(...node.keys);
      leftSibling.next = node.next;
    } else {
      leftSibling.keys.push(parent.keys[nodeIndex - 1]);
      leftSibling.keys.push(...node.keys);
      leftSibling.pointers.push(...node.pointers);

      // 更新子节点的父指针
      node.pointers.forEach(pointerId => {
        if (pointerId) {
          const child = this.allNodes.get(pointerId);
          if (child) child.parent = leftSibling.id;
        }
      });
    }

    parent.keys.splice(nodeIndex - 1, 1);
    parent.pointers.splice(nodeIndex, 1);
    this.allNodes.delete(node.id);
  }

  // 检查节点首个key是否发生变化
  private isFirstKeyChanged(node: BPlusTreeNode): boolean {
    // 这个函数需要在实际使用时根据上下文来判断
    // 暂时返回false，在具体的插入/删除操作中会有更精确的判断
    return false;
  }

  // 递归向上更新祖先节点的分割键
  private *updateAncestorKeys(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    if (!node.parent || node.keys.length === 0) return;

    const newFirstKey = node.keys[0];
    this.updateParentIndexKey(node.id, newFirstKey);

    const parent = this.allNodes.get(node.parent)!;
    yield { type: 'update_parent', nodeId: parent.id, newKey: newFirstKey };

    // 递归向上更新
    yield* this.updateAncestorKeys(parent);
  }

  // 更新父节点中指向该节点的索引键
  private updateParentIndexKey(nodeId: string, newFirstKey: number): void {
    const node = this.allNodes.get(nodeId);
    if (!node || !node.parent) {
      return;
    }

    const parent = this.allNodes.get(node.parent);
    if (!parent) {
      return;
    }

    // 找到父节点中指向该节点的指针位置
    const pointerIndex = parent.pointers.indexOf(nodeId);
    if (pointerIndex === -1) {
      return;
    }

    // 更新相应的索引键
    // 对于内部节点，第i个指针对应第i-1个键（第0个指针没有对应的键）
    if (pointerIndex > 0 && pointerIndex - 1 < parent.keys.length) {
      parent.keys[pointerIndex - 1] = newFirstKey;
    }
  }







  // 获取所有节点（用于可视化）
  public getAllNodes(): BPlusTreeNode[] {
    return Array.from(this.allNodes.values());
  }

  // 获取根节点
  public getRoot(): BPlusTreeNode | null {
    return this.root;
  }

  // 高效查找键是否存在
  public find(key: number): boolean {
    if (!this.root) {
      return false;
    }

    // 找到最左边的叶子节点
    let current = this.root;
    while (!current.isLeaf) {
      const firstChildId = current.pointers[0];
      if (!firstChildId || !this.allNodes.has(firstChildId)) {
        return false;
      }
      current = this.allNodes.get(firstChildId)!;
    }

    // 遍历所有叶子节点，查找指定键
    while (current) {
      // 检查当前叶子节点是否包含目标键
      if (current.keys.includes(key)) {
        return true;
      }

      // 移动到下一个叶子节点
      if (current.next && this.allNodes.has(current.next)) {
        current = this.allNodes.get(current.next)!;
      } else {
        break;
      }
    }

    return false;
  }

  // 获取所有键的排序数组
  public getAllKeys(): number[] {
    const keys: number[] = [];

    if (!this.root) {
      return keys;
    }

    // 找到最左边的叶子节点
    let current = this.root;
    while (!current.isLeaf) {
      const firstChildId = current.pointers[0];
      if (!firstChildId || !this.allNodes.has(firstChildId)) {
        break;
      }
      current = this.allNodes.get(firstChildId)!;
    }

    // 遍历所有叶子节点，收集键
    while (current) {
      // 添加当前叶子节点的所有键
      current.keys.forEach(key => {
        keys.push(key);
      });

      // 移动到下一个叶子节点
      if (current.next && this.allNodes.has(current.next)) {
        current = this.allNodes.get(current.next)!;
      } else {
        break;
      }
    }

    return keys.sort((a, b) => a - b);
  }

  // 清空树
  public clear(): void {
    this.root = null;
    this.allNodes.clear();
    this.nodeCounter = 0;
  }
}
